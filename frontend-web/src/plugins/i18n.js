/**
 * Vue I18n Plugin Configuration
 * 
 * This file configures Vue I18n for internationalization support
 * in the Smart Factory WMS frontend application.
 */

import { createI18n } from 'vue-i18n'

// Import locale messages
const messages = {
  en: {
    app: {
      name: 'Smart Factory WMS',
      subtitle: 'Warehouse Management System'
    },
    auth: {
      logout_success: 'Successfully logged out',
      logout_error: 'Error occurred during logout'
    },
    common: {
      guest: 'Guest'
    }
  },
  ja: {
    app: {
      name: 'スマートファクトリーWMS',
      subtitle: '倉庫管理システム'
    },
    auth: {
      logout_success: 'ログアウトしました',
      logout_error: 'ログアウト中にエラーが発生しました'
    },
    common: {
      guest: 'ゲスト'
    }
  },
  zh: {
    app: {
      name: '智能工厂WMS',
      subtitle: '仓库管理系统'
    },
    auth: {
      logout_success: '成功登出',
      logout_error: '登出时发生错误'
    },
    common: {
      guest: '访客'
    }
  },
  vi: {
    app: {
      name: 'Smart Factory WMS',
      subtitle: '<PERSON><PERSON> thống quản lý kho'
    },
    auth: {
      logout_success: 'Đăng xuất thành công',
      logout_error: 'Có lỗi xảy ra khi đăng xuất'
    },
    common: {
      guest: 'Khách'
    }
  }
}

// Create I18n instance
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages,
  globalInjection: true
})

export default i18n
